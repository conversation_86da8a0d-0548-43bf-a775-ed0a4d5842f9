
import { GoogleGenAI, Type } from "@google/genai";

// Ensure API_KEY is available in the environment.
// In a real setup, this would be managed by the deployment environment.
const API_KEY = process.env.API_KEY;
if (!API_KEY) {
  // In a real app, you might want to handle this more gracefully.
  // For this context, we'll throw an error if the key is missing.
  console.warn("API key not found. Please set the API_KEY environment variable.");
}

const ai = new GoogleGenAI({ apiKey: API_KEY! });

const titleSchema = {
  type: Type.OBJECT,
  properties: {
    vocalTitle: {
      type: Type.STRING,
      description: 'Un título creativo y artístico para la pista de solo voz. Debe ser corto y evocador.',
    },
    musicTitle: {
      type: Type.STRING,
      description: 'Un título creativo y artístico para la pista instrumental (solo música). Debe ser corto y evocador.',
    },
  },
  required: ['vocalTitle', 'musicTitle'],
};

export async function generateTrackTitles(fileName: string): Promise<{ vocalTitle: string; musicTitle: string }> {
  try {
    const prompt = `Basado en el nombre de archivo de una canción, "${fileName}", genera dos títulos creativos y cortos: uno para la pista de voz aislada y otro para la pista instrumental.`;

    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        responseSchema: titleSchema,
        temperature: 0.8,
      },
    });

    const text = response.text;
    const data = JSON.parse(text);

    if (data && data.vocalTitle && data.musicTitle) {
      return {
        vocalTitle: data.vocalTitle,
        musicTitle: data.musicTitle,
      };
    } else {
      console.error("Respuesta JSON inválida de la API:", data);
      throw new Error("La API no devolvió los títulos esperados.");
    }

  } catch (error) {
    console.error("Error al llamar a la API de Gemini:", error);
    throw new Error("No se pudieron generar los títulos de las pistas.");
  }
}
