
import React, { useRef, useState } from 'react';
import { UploadIcon } from './icons/UploadIcon';
import { SpinnerIcon } from './icons/SpinnerIcon';

interface FileUploaderProps {
  onFileSelect: (file: File) => void;
  isLoading: boolean;
  error: string | null;
}

const FileUploader: React.FC<FileUploaderProps> = ({ onFileSelect, isLoading, error }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onFileSelect(file);
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(true);
  };
  
  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);
  };
  
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);
    const file = event.dataTransfer.files?.[0];
    if (file && file.type === 'audio/mpeg') {
      onFileSelect(file);
    } else {
      // Simple validation feedback, could be improved
      alert("Por favor, sube un archivo MP3.");
    }
  };

  return (
    <div className="p-8 transition-all duration-300">
      <div 
        className={`flex flex-col items-center justify-center p-10 border-2 border-dashed rounded-lg transition-colors duration-300 ${isDragging ? 'border-purple-400 bg-zinc-700/50' : 'border-zinc-600 hover:border-purple-500'}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          className="hidden"
          accept="audio/mpeg"
          disabled={isLoading}
        />
        {isLoading ? (
          <div className="text-center">
            <SpinnerIcon className="w-12 h-12 text-purple-400 animate-spin mx-auto" />
            <p className="mt-4 text-lg font-medium text-zinc-300">Procesando audio...</p>
            <p className="text-sm text-zinc-400">Generando títulos con IA. Esto puede tardar un momento.</p>
          </div>
        ) : (
          <div className="text-center">
            <UploadIcon className="w-12 h-12 mx-auto text-zinc-400" />
            <p className="mt-4 text-lg font-medium text-zinc-300">
              Arrastra y suelta tu archivo MP3 aquí
            </p>
            <p className="mt-1 text-sm text-zinc-500">o</p>
            <button
              onClick={handleClick}
              disabled={isLoading}
              className="mt-2 px-6 py-2 bg-purple-600 text-white font-semibold rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-zinc-800 focus:ring-purple-500 transition-all duration-200"
            >
              Seleccionar Archivo
            </button>
          </div>
        )}
      </div>
      {error && <p className="mt-4 text-center text-red-400">{error}</p>}
    </div>
  );
};

export default FileUploader;
