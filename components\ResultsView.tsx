
import React from 'react';
import { MicIcon } from './icons/MicIcon';
import { MusicIcon } from './icons/MusicIcon';
import { DownloadIcon } from './icons/DownloadIcon';

interface ResultsViewProps {
  fileName: string;
  vocalTitle: string;
  musicTitle: string;
  onReset: () => void;
}

const TrackCard: React.FC<{
  icon: React.ReactNode;
  trackType: string;
  title: string;
}> = ({ icon, trackType, title }) => (
  <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700 flex flex-col items-center text-center transition-all duration-300 hover:border-purple-500 hover:bg-zinc-700/50">
    <div className="w-16 h-16 bg-zinc-700 rounded-full flex items-center justify-center mb-4 border border-zinc-600">
      {icon}
    </div>
    <p className="text-sm font-medium text-purple-400">{trackType}</p>
    <h3 className="text-xl font-bold text-slate-100 mt-1 mb-4 truncate w-full" title={title}>
      {title}
    </h3>
    <button 
      disabled 
      className="w-full mt-auto px-4 py-2 bg-zinc-600 text-zinc-400 font-semibold rounded-md flex items-center justify-center gap-2 cursor-not-allowed"
      title="La descarga está deshabilitada en esta demostración"
    >
      <DownloadIcon className="w-5 h-5" />
      <span>Descargar (Simulado)</span>
    </button>
  </div>
);

const ResultsView: React.FC<ResultsViewProps> = ({ fileName, vocalTitle, musicTitle, onReset }) => {
  return (
    <div className="p-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-slate-100">¡Procesamiento Completo!</h2>
        <p className="text-zinc-400 mt-1">
          Títulos generados por IA para <span className="font-semibold text-purple-400">{fileName}</span>
        </p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <TrackCard 
          icon={<MicIcon className="w-8 h-8 text-purple-400" />}
          trackType="Pista de Voz"
          title={vocalTitle}
        />
        <TrackCard 
          icon={<MusicIcon className="w-8 h-8 text-purple-400" />}
          trackType="Pista de Música"
          title={musicTitle}
        />
      </div>
      <div className="text-center">
        <button
          onClick={onReset}
          className="px-8 py-2 bg-purple-600 text-white font-semibold rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-zinc-800 focus:ring-purple-500 transition-all duration-200"
        >
          Procesar Otro Archivo
        </button>
      </div>
    </div>
  );
};

export default ResultsView;
