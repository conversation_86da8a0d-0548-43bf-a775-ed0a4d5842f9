
import React, { useState, useCallback } from 'react';
import { generateTrackTitles } from './services/geminiService';
import type { AppStatus } from './types';
import FileUploader from './components/FileUploader';
import ResultsView from './components/ResultsView';

function App() {
  const [status, setStatus] = useState<AppStatus>('idle');
  const [fileName, setFileName] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [titles, setTitles] = useState<{ vocalTitle: string; musicTitle: string } | null>(null);

  const handleFileSelect = useCallback(async (file: File) => {
    if (!file) return;

    setStatus('processing');
    setFileName(file.name);
    setError(null);

    try {
      // Simulate audio processing while fetching titles from Gemini
      const generatedTitles = await generateTrackTitles(file.name);
      setTitles(generatedTitles);
      
      // Simulate a delay for the "audio separation" part to make it feel real
      await new Promise(resolve => setTimeout(resolve, 2000));

      setStatus('success');
    } catch (err) {
      console.error(err);
      setError('Hubo un error al procesar el archivo. Por favor, inténtalo de nuevo.');
      setStatus('error');
    }
  }, []);

  const handleReset = useCallback(() => {
    setStatus('idle');
    setFileName(null);
    setTitles(null);
    setError(null);
  }, []);

  const renderContent = () => {
    switch (status) {
      case 'success':
        return (
          <ResultsView
            fileName={fileName!}
            vocalTitle={titles?.vocalTitle || "Pista Vocal"}
            musicTitle={titles?.musicTitle || "Pista Instrumental"}
            onReset={handleReset}
          />
        );
      case 'idle':
      case 'processing':
      case 'error':
      default:
        return (
          <FileUploader
            onFileSelect={handleFileSelect}
            isLoading={status === 'processing'}
            error={error}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-zinc-900 text-slate-100 flex flex-col items-center justify-center p-4">
      <main className="w-full max-w-2xl mx-auto">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-500">
            Separador de Audio IA
          </h1>
          <p className="mt-2 text-lg text-zinc-400">
            Sube un MP3 y nosotros generamos títulos creativos para las pistas de voz y música.
          </p>
        </header>
        
        <div className="bg-zinc-800/50 border border-zinc-700 rounded-xl shadow-2xl shadow-purple-500/10 backdrop-blur-sm">
          {renderContent()}
        </div>
        
        <footer className="text-center mt-8 text-zinc-500 text-sm">
          <p>
            <span className="font-semibold">Nota:</span> La separación de audio es una simulación para esta demostración.
            La funcionalidad principal mostrada es la generación de títulos mediante la API de Gemini.
          </p>
        </footer>
      </main>
    </div>
  );
}

export default App;
